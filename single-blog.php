<?php
/**
 * The template for displaying single blog posts
 *
 * @package tendeal
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container mt-4">
        <?php while (have_posts()) : the_post(); ?>
            
            <!-- Breadcrumb -->
            <div class="row mb-3">
                <div class="col-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb bg-white p-3 rounded">
                            <li class="breadcrumb-item">
                                <a href="<?php echo home_url(); ?>" class="text-decoration-none">
                                    <i class="fas fa-home"></i> Home
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="<?php echo get_permalink(get_page_by_path('blogs')); ?>" class="text-decoration-none">Blog</a>
                            </li>
                            <?php
                            $categories = get_the_category();
                            if (!empty($categories)) :
                            ?>
                                <li class="breadcrumb-item">
                                    <a href="<?php echo get_category_link($categories[0]->term_id); ?>" class="text-decoration-none">
                                        <?php echo $categories[0]->name; ?>
                                    </a>
                                </li>
                            <?php endif; ?>
                            <li class="breadcrumb-item active" aria-current="page">
                                <?php echo wp_trim_words(get_the_title(), 5, '...'); ?>
                            </li>
                        </ol>
                    </nav>
                </div>
            </div>

            <!-- Blog Post -->
            <div class="row">
                <div class="col-lg-8">
                    <article id="post-<?php the_ID(); ?>" <?php post_class('blog-post bg-white rounded p-4 mb-4'); ?>>
                        
                        <!-- Post Header -->
                        <header class="entry-header mb-4">
                            <h1 class="entry-title mb-3"><?php the_title(); ?></h1>
                            
                            <div class="entry-meta d-flex flex-wrap align-items-center mb-3">
                                <div class="author-info d-flex align-items-center me-4 mb-2">
                                    <?php echo get_avatar(get_the_author_meta('ID'), 32, '', '', array('class' => 'rounded-circle me-2')); ?>
                                    <div>
                                        <small class="text-muted d-block">By</small>
                                        <strong><?php echo get_the_author(); ?></strong>
                                    </div>
                                </div>
                                
                                <div class="post-date me-4 mb-2">
                                    <small class="text-muted d-block">Published</small>
                                    <strong><?php echo get_the_date('M j, Y'); ?></strong>
                                </div>
                                
                                <div class="reading-time me-4 mb-2">
                                    <small class="text-muted d-block">Reading time</small>
                                    <strong><?php echo ceil(str_word_count(get_the_content()) / 200); ?> min read</strong>
                                </div>
                                
                                <?php if (!empty($categories)) : ?>
                                <div class="post-category mb-2">
                                    <small class="text-muted d-block">Category</small>
                                    <a href="<?php echo get_category_link($categories[0]->term_id); ?>" class="badge bg-primary text-decoration-none">
                                        <?php echo $categories[0]->name; ?>
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </header>

                        <!-- Featured Image -->
                        <?php if (has_post_thumbnail()) : ?>
                        <div class="entry-image mb-4">
                            <?php the_post_thumbnail('large', array('class' => 'img-fluid rounded w-100')); ?>
                        </div>
                        <?php endif; ?>

                        <!-- Post Content -->
                        <div class="entry-content">
                            <?php
                            the_content();
                            
                            wp_link_pages(array(
                                'before' => '<div class="page-links mt-4"><span class="page-links-title">' . __('Pages:', 'tendeal') . '</span>',
                                'after' => '</div>',
                                'link_before' => '<span class="page-number">',
                                'link_after' => '</span>',
                            ));
                            ?>
                        </div>

                        <!-- Post Tags -->
                        <?php
                        $tags = get_the_tags();
                        if ($tags) :
                        ?>
                        <div class="entry-tags mt-4 pt-3 border-top">
                            <h6 class="mb-2">Tags:</h6>
                            <div class="tags-list">
                                <?php foreach ($tags as $tag) : ?>
                                    <a href="<?php echo get_tag_link($tag->term_id); ?>" class="badge bg-light text-dark text-decoration-none me-2 mb-2">
                                        #<?php echo $tag->name; ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Social Share -->
                        <div class="social-share mt-4 pt-3 border-top">
                            <h6 class="mb-3">Share this post:</h6>
                            <div class="share-buttons">
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" 
                                   target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                    <i class="fab fa-facebook-f"></i> Facebook
                                </a>
                                <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" 
                                   target="_blank" class="btn btn-outline-info btn-sm me-2 mb-2">
                                    <i class="fab fa-twitter"></i> Twitter
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(get_permalink()); ?>" 
                                   target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                    <i class="fab fa-linkedin-in"></i> LinkedIn
                                </a>
                                <a href="https://wa.me/?text=<?php echo urlencode(get_the_title() . ' - ' . get_permalink()); ?>" 
                                   target="_blank" class="btn btn-outline-success btn-sm me-2 mb-2">
                                    <i class="fab fa-whatsapp"></i> WhatsApp
                                </a>
                            </div>
                        </div>

                        <!-- Author Bio -->
                        <div class="author-bio mt-4 pt-4 border-top">
                            <div class="d-flex">
                                <div class="author-avatar me-3">
                                    <?php echo get_avatar(get_the_author_meta('ID'), 64, '', '', array('class' => 'rounded-circle')); ?>
                                </div>
                                <div class="author-info flex-grow-1">
                                    <h6 class="mb-1"><?php echo get_the_author(); ?></h6>
                                    <p class="text-muted mb-2"><?php echo get_the_author_meta('description') ?: 'Content writer and blogger.'; ?></p>
                                    <div class="author-links">
                                        <?php if (get_the_author_meta('user_url')) : ?>
                                            <a href="<?php echo get_the_author_meta('user_url'); ?>" class="text-decoration-none me-3" target="_blank">
                                                <i class="fas fa-globe"></i> Website
                                            </a>
                                        <?php endif; ?>
                                        <a href="<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>" class="text-decoration-none">
                                            <i class="fas fa-user"></i> View all posts
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </article>

                    <!-- Post Navigation -->
                    <div class="post-navigation bg-white rounded p-4 mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <?php
                                $prev_post = get_previous_post();
                                if ($prev_post) :
                                ?>
                                    <div class="nav-previous">
                                        <small class="text-muted d-block mb-1">
                                            <i class="fas fa-chevron-left"></i> Previous Post
                                        </small>
                                        <a href="<?php echo get_permalink($prev_post->ID); ?>" class="text-decoration-none">
                                            <strong><?php echo wp_trim_words($prev_post->post_title, 8, '...'); ?></strong>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <?php
                                $next_post = get_next_post();
                                if ($next_post) :
                                ?>
                                    <div class="nav-next">
                                        <small class="text-muted d-block mb-1">
                                            Next Post <i class="fas fa-chevron-right"></i>
                                        </small>
                                        <a href="<?php echo get_permalink($next_post->ID); ?>" class="text-decoration-none">
                                            <strong><?php echo wp_trim_words($next_post->post_title, 8, '...'); ?></strong>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Comments -->
                    <?php
                    if (comments_open() || get_comments_number()) :
                        // Use custom blog comments template
                        get_template_part('comments', 'blog');
                    endif;
                    ?>

                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <?php get_template_part('template-parts/blog-sidebar'); ?>
                </div>
            </div>

        <?php endwhile; ?>
    </div>
</main>

<?php
get_footer();
?>
