/**
 * Blog Functionality JavaScript
 */

jQuery(document).ready(function($) {
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Category filter functionality (if AJAX is needed)
    $('.category-filter').on('click', function(e) {
        e.preventDefault();
        
        var $this = $(this);
        var category = $this.data('category');
        
        // Update active state
        $('.category-filter').removeClass('active');
        $this.addClass('active');
        
        // If it's "all" category, just redirect to blogs page
        if (category === 'all') {
            window.location.href = $this.attr('href');
            return;
        }
        
        // For specific categories, redirect to category page
        window.location.href = $this.attr('href');
    });

    // Newsletter subscription
    $('.newsletter-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $email = $form.find('input[type="email"]');
        var $button = $form.find('button[type="submit"]');
        var email = $email.val();
        
        if (!email || !isValidEmail(email)) {
            showNotification('Please enter a valid email address.', 'error');
            return;
        }
        
        // Show loading state
        var originalText = $button.html();
        $button.html('<span class="loading"></span> Subscribing...').prop('disabled', true);
        
        // Simulate API call (replace with actual newsletter subscription logic)
        setTimeout(function() {
            $button.html('<i class="fas fa-check me-2"></i> Subscribed!').removeClass('btn-light').addClass('btn-success');
            $email.val('');
            showNotification('Thank you for subscribing to our newsletter!', 'success');
            
            // Reset button after 3 seconds
            setTimeout(function() {
                $button.html(originalText).removeClass('btn-success').addClass('btn-light').prop('disabled', false);
            }, 3000);
        }, 2000);
    });

    // Social share functionality
    $('.social-share a').on('click', function(e) {
        e.preventDefault();
        
        var url = $(this).attr('href');
        var width = 600;
        var height = 400;
        var left = (screen.width / 2) - (width / 2);
        var top = (screen.height / 2) - (height / 2);
        
        window.open(url, 'share', 'width=' + width + ',height=' + height + ',left=' + left + ',top=' + top + ',scrollbars=yes,resizable=yes');
    });

    // Reading progress bar
    if ($('.blog-post').length) {
        var $progressBar = $('<div class="reading-progress"><div class="reading-progress-bar"></div></div>');
        $('body').prepend($progressBar);
        
        $(window).on('scroll', function() {
            var scrollTop = $(window).scrollTop();
            var docHeight = $(document).height() - $(window).height();
            var scrollPercent = (scrollTop / docHeight) * 100;
            
            $('.reading-progress-bar').css('width', scrollPercent + '%');
        });
    }

    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        var imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    var img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(function(img) {
            imageObserver.observe(img);
        });
    }

    // Search functionality enhancement
    $('.sidebar-widget form[role="search"]').on('submit', function(e) {
        var searchTerm = $(this).find('input[name="s"]').val().trim();
        if (!searchTerm) {
            e.preventDefault();
            showNotification('Please enter a search term.', 'warning');
        }
    });

    // Copy link functionality
    function addCopyLinkButton() {
        if ($('.blog-post').length) {
            var copyButton = '<button class="btn btn-outline-secondary btn-sm copy-link-btn" title="Copy link to this post">' +
                            '<i class="fas fa-link"></i> Copy Link</button>';
            $('.social-share .share-buttons').append(copyButton);
        }
    }

    $(document).on('click', '.copy-link-btn', function(e) {
        e.preventDefault();
        
        var url = window.location.href;
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(function() {
                showNotification('Link copied to clipboard!', 'success');
            });
        } else {
            // Fallback for older browsers
            var textArea = document.createElement('textarea');
            textArea.value = url;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showNotification('Link copied to clipboard!', 'success');
        }
    });

    // Initialize copy link button
    addCopyLinkButton();

    // Estimated reading time calculation
    function calculateReadingTime() {
        if ($('.entry-content').length) {
            var content = $('.entry-content').text();
            var wordCount = content.split(/\s+/).length;
            var readingTime = Math.ceil(wordCount / 200); // Average reading speed: 200 words per minute
            
            $('.reading-time strong').text(readingTime + ' min read');
        }
    }

    calculateReadingTime();

    // Table of contents generation
    function generateTableOfContents() {
        var headings = $('.entry-content').find('h2, h3, h4, h5, h6');
        
        if (headings.length > 2) {
            var toc = '<div class="table-of-contents bg-light p-3 rounded mb-4">' +
                     '<h6 class="mb-3"><i class="fas fa-list me-2"></i>Table of Contents</h6>' +
                     '<ul class="list-unstyled mb-0">';
            
            headings.each(function(index) {
                var $heading = $(this);
                var id = 'heading-' + index;
                var text = $heading.text();
                var level = parseInt($heading.prop('tagName').substring(1));
                
                $heading.attr('id', id);
                
                var indent = (level - 2) * 20; // Indent based on heading level
                toc += '<li style="margin-left: ' + indent + 'px; margin-bottom: 5px;">' +
                       '<a href="#' + id + '" class="text-decoration-none">' + text + '</a></li>';
            });
            
            toc += '</ul></div>';
            
            $('.entry-content').prepend(toc);
        }
    }

    generateTableOfContents();

    // Utility functions
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showNotification(message, type) {
        var alertClass = 'alert-info';
        var icon = 'fas fa-info-circle';
        
        switch(type) {
            case 'success':
                alertClass = 'alert-success';
                icon = 'fas fa-check-circle';
                break;
            case 'error':
                alertClass = 'alert-danger';
                icon = 'fas fa-exclamation-circle';
                break;
            case 'warning':
                alertClass = 'alert-warning';
                icon = 'fas fa-exclamation-triangle';
                break;
        }
        
        var notification = '<div class="alert ' + alertClass + ' alert-dismissible fade show notification-toast" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">' +
                          '<i class="' + icon + ' me-2"></i>' + message +
                          '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                          '</div>';
        
        $('body').append(notification);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $('.notification-toast').alert('close');
        }, 5000);
    }

    // Print functionality
    function addPrintButton() {
        if ($('.blog-post').length) {
            var printButton = '<button class="btn btn-outline-secondary btn-sm print-btn me-2 mb-2" title="Print this post">' +
                             '<i class="fas fa-print"></i> Print</button>';
            $('.social-share .share-buttons').prepend(printButton);
        }
    }

    $(document).on('click', '.print-btn', function(e) {
        e.preventDefault();
        window.print();
    });

    addPrintButton();

    // Back to top button
    var backToTopButton = '<button class="btn btn-primary back-to-top" style="position: fixed; bottom: 20px; right: 20px; z-index: 9999; display: none; border-radius: 50%; width: 50px; height: 50px;">' +
                         '<i class="fas fa-chevron-up"></i></button>';
    $('body').append(backToTopButton);

    $(window).on('scroll', function() {
        if ($(window).scrollTop() > 300) {
            $('.back-to-top').fadeIn();
        } else {
            $('.back-to-top').fadeOut();
        }
    });

    $(document).on('click', '.back-to-top', function(e) {
        e.preventDefault();
        $('html, body').animate({scrollTop: 0}, 600);
    });

});

// Add reading progress bar styles
jQuery(document).ready(function($) {
    if ($('.blog-post').length) {
        var progressBarStyles = '<style>' +
            '.reading-progress {' +
                'position: fixed;' +
                'top: 0;' +
                'left: 0;' +
                'width: 100%;' +
                'height: 3px;' +
                'background-color: rgba(0,0,0,0.1);' +
                'z-index: 9999;' +
            '}' +
            '.reading-progress-bar {' +
                'height: 100%;' +
                'background-color: var(--bs-primary);' +
                'transition: width 0.3s ease;' +
            '}' +
            '</style>';
        $('head').append(progressBarStyles);
    }
});
