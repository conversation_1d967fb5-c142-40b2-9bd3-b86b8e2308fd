/* Blog Styles */

/* Blog Page Styles */
.page-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

.featured-post {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.featured-post:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.featured-image-placeholder {
    min-height: 300px;
}

.blog-categories {
    border: 1px solid #dee2e6;
}

.category-filter {
    transition: all 0.3s ease;
}

.category-filter:hover,
.category-filter.active {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white !important;
}

.blog-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #dee2e6;
}

.blog-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.blog-image-placeholder {
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%), 
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.blog-title a:hover {
    color: var(--bs-primary) !important;
}

.blog-meta a:hover {
    color: var(--bs-primary) !important;
}

.no-posts {
    border: 2px dashed #dee2e6;
}

/* Pagination Styles */
.pagination-wrapper .page-numbers {
    display: inline-block;
    padding: 0.5rem 0.75rem;
    margin: 0 0.25rem;
    color: var(--bs-primary);
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.pagination-wrapper .page-numbers:hover,
.pagination-wrapper .page-numbers.current {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

.pagination-wrapper .page-numbers.dots {
    border: none;
    background: none;
    color: #6c757d;
}

/* Single Blog Post Styles */
.blog-post {
    border: 1px solid #dee2e6;
}

.entry-title {
    color: var(--bs-dark);
    font-weight: 700;
    line-height: 1.2;
}

.entry-meta {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;
}

.author-info img {
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.entry-content {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #495057;
}

.entry-content h2,
.entry-content h3,
.entry-content h4,
.entry-content h5,
.entry-content h6 {
    color: var(--bs-dark);
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.entry-content p {
    margin-bottom: 1.5rem;
}

.entry-content img {
    border-radius: 0.5rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.entry-content blockquote {
    border-left: 4px solid var(--bs-primary);
    background-color: #f8f9fa;
    padding: 1rem 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    border-radius: 0 0.5rem 0.5rem 0;
}

.entry-content code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.9em;
    color: #e83e8c;
}

.entry-content pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    border: 1px solid #dee2e6;
}

.page-links {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    text-align: center;
}

.page-links .page-number {
    display: inline-block;
    padding: 0.5rem 0.75rem;
    margin: 0 0.25rem;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    text-decoration: none;
    color: var(--bs-primary);
    transition: all 0.3s ease;
}

.page-links .page-number:hover {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

.entry-tags .badge {
    font-size: 0.9em;
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;
}

.entry-tags .badge:hover {
    background-color: var(--bs-primary) !important;
    color: white !important;
}

.social-share .btn {
    transition: all 0.3s ease;
}

.social-share .btn:hover {
    transform: translateY(-2px);
}

.author-bio {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin: 0 -1.5rem;
}

.author-bio img {
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.post-navigation {
    border: 1px solid #dee2e6;
}

.nav-previous,
.nav-next {
    transition: all 0.3s ease;
}

.nav-previous:hover,
.nav-next:hover {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.5rem;
}

/* Sidebar Styles */
.sidebar-widget {
    border: 1px solid #dee2e6;
    transition: transform 0.3s ease;
}

.sidebar-widget:hover {
    transform: translateY(-2px);
}

.widget-title {
    color: var(--bs-dark);
    font-weight: 600;
    position: relative;
    padding-bottom: 0.5rem;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--bs-primary);
}

.recent-post-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

.recent-post-item a:hover {
    color: var(--bs-primary) !important;
}

.category-item a:hover,
.archive-item a:hover {
    color: var(--bs-primary) !important;
}

.tags-cloud .badge:hover {
    background-color: var(--bs-primary) !important;
    color: white !important;
}

.newsletter-form .btn:hover {
    transform: translateY(-2px);
}

.social-links .btn {
    transition: all 0.3s ease;
}

.social-links .btn:hover {
    transform: translateY(-2px);
}

.ad-placeholder {
    transition: all 0.3s ease;
}

.ad-placeholder:hover {
    border-color: var(--bs-primary) !important;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .featured-post .row {
        flex-direction: column-reverse;
    }
    
    .featured-image {
        min-height: 200px;
    }
    
    .entry-meta {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .entry-meta > div {
        margin-bottom: 0.5rem;
    }
    
    .author-bio {
        margin: 0;
        padding: 1rem;
    }
    
    .author-bio .d-flex {
        flex-direction: column;
        text-align: center;
    }
    
    .author-bio .author-avatar {
        margin: 0 auto 1rem auto;
    }
}

/* Comments Styles */
.comment-list {
    list-style: none;
    padding: 0;
}

.comment-body {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #dee2e6;
}

.comment-author img {
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.comment-reply-link {
    color: var(--bs-primary);
    text-decoration: none;
    font-size: 0.9rem;
}

.comment-reply-link:hover {
    text-decoration: underline;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(234, 156, 0, 0.3);
    border-radius: 50%;
    border-top-color: var(--bs-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
