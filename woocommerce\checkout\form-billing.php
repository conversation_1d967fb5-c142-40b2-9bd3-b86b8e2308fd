<?php
/**
 * Checkout billing information form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-billing.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.6.0
 * @global WC_Checkout $checkout
 */

defined( 'ABSPATH' ) || exit;

// Get customer data if logged in
$customer_id = get_current_user_id();
$has_saved_addresses = false;
$saved_addresses = array();
$customer_name = '';

if ( $customer_id ) {
	$customer = new WC_Customer( $customer_id );
	$user_info = get_userdata( $customer_id );
	$customer_name = trim( $customer->get_first_name() . ' ' . $customer->get_last_name() );
	if ( empty( $customer_name ) ) {
		$customer_name = $user_info->display_name;
	}

	// Get saved addresses using a proper method
	$saved_addresses = tendeal_get_user_saved_addresses( $customer_id );
	$has_saved_addresses = !empty( $saved_addresses );

	// Fallback to basic address check
	if ( !$has_saved_addresses ) {
		$billing_address_1 = get_user_meta( $customer_id, 'billing_address_1', true );
		$billing_city = get_user_meta( $customer_id, 'billing_city', true );
		$has_saved_addresses = !empty( $billing_address_1 ) || !empty( $billing_city );
		
		if ( $has_saved_addresses ) {
			// Create a basic saved address from billing data
			$saved_addresses = array(
				array(
					'id' => 'billing',
					'title' => __( 'Billing Address', 'tendeal' ),
					'is_default' => true,
					'formatted_address' => WC()->countries->get_formatted_address( array(
						'first_name' => get_user_meta( $customer_id, 'billing_first_name', true ),
						'last_name' => get_user_meta( $customer_id, 'billing_last_name', true ),
						'company' => get_user_meta( $customer_id, 'billing_company', true ),
						'address_1' => get_user_meta( $customer_id, 'billing_address_1', true ),
						'address_2' => get_user_meta( $customer_id, 'billing_address_2', true ),
						'city' => get_user_meta( $customer_id, 'billing_city', true ),
						'state' => get_user_meta( $customer_id, 'billing_state', true ),
						'postcode' => get_user_meta( $customer_id, 'billing_postcode', true ),
						'country' => get_user_meta( $customer_id, 'billing_country', true ),
					)),
					'phone' => get_user_meta( $customer_id, 'billing_phone', true )
				)
			);
		}
	}
}
?>

<div class="woocommerce-billing-fields">
	<?php if ( wc_ship_to_billing_address_only() && WC()->cart->needs_shipping() ) : ?>
		<h3><?php esc_html_e( 'Billing &amp; Shipping', 'woocommerce' ); ?></h3>
	<?php else : ?>
		<h3><?php esc_html_e( 'Billing details', 'woocommerce' ); ?></h3>
	<?php endif; ?>

	<?php if ( $has_saved_addresses ) : ?>
		<!-- Saved Addresses Section -->
		<div class="saved-addresses-section">
			<div class="section-header">
				<h4 class="section-title">
					<i data-feather="map-pin" class="feather-sm me-2"></i>
					<?php esc_html_e( 'Select Delivery Address', 'tendeal' ); ?>
				</h4>
				<p class="section-description"><?php esc_html_e( 'Choose from your saved addresses or add a new one', 'tendeal' ); ?></p>
			</div>

			<div class="address-list">
				<?php foreach ( $saved_addresses as $address ) : ?>
				<div class="address-card checkout-address-card" 
					 data-address-id="<?php echo esc_attr( $address['id'] ); ?>" 
					 data-address-type="billing"
					 <?php echo $address['is_default'] ? 'data-default="true"' : ''; ?>>
					
					<div class="address-card-header">
						<div class="address-icon">
							<i data-feather="<?php echo $address['is_default'] ? 'home' : 'map-pin'; ?>" class="feather-sm"></i>
						</div>
						<div class="address-title">
							<h5><?php echo esc_html( $address['title'] ); ?></h5>
							<?php if ( $address['is_default'] ) : ?>
							<span class="default-badge">
								<i data-feather="check-circle" class="feather-xs me-1"></i>
								<?php esc_html_e( 'Default', 'tendeal' ); ?>
							</span>
							<?php endif; ?>
						</div>
						<div class="address-selector">
							<input type="radio" 
								   name="selected_address" 
								   value="<?php echo esc_attr( $address['id'] ); ?>"
								   id="address_<?php echo esc_attr( $address['id'] ); ?>"
								   <?php echo $address['is_default'] ? 'checked' : ''; ?>>
							<label for="address_<?php echo esc_attr( $address['id'] ); ?>"></label>
						</div>
					</div>

					<div class="address-card-body">
						<div class="address-details">
							<?php echo wp_kses_post( $address['formatted_address'] ); ?>
						</div>
						<?php if ( !empty( $address['phone'] ) ) : ?>
						<div class="address-contact">
							<i data-feather="phone" class="feather-xs me-1"></i>
							<?php echo esc_html( $address['phone'] ); ?>
						</div>
						<?php endif; ?>
					</div>
				</div>
				<?php endforeach; ?>

				<!-- Add New Address Option -->
				<div class="address-card add-new-address" data-address-id="new">
					<div class="address-card-body text-center">
						<div class="add-address-icon">
							<i data-feather="plus-circle" class="feather-lg"></i>
						</div>
						<h5 class="add-address-title"><?php esc_html_e( 'Add New Address', 'tendeal' ); ?></h5>
						<p class="add-address-description"><?php esc_html_e( 'Enter a new delivery address', 'tendeal' ); ?></p>
						<div class="address-selector">
							<input type="radio" 
								   name="selected_address" 
								   value="new"
								   id="address_new">
							<label for="address_new"></label>
						</div>
					</div>
				</div>
			</div>

			<!-- Hidden input to track selected address -->
			<input type="hidden" id="selected_address_id" name="selected_address_id" value="<?php echo !empty($saved_addresses) && $saved_addresses[0]['is_default'] ? $saved_addresses[0]['id'] : 'new'; ?>" />
		</div>
	<?php endif; ?>

	<div class="woocommerce-billing-fields__field-wrapper<?php echo $has_saved_addresses ? ' hidden-form' : ''; ?>">
		<?php
		$fields = $checkout->get_checkout_fields( 'billing' );

		foreach ( $fields as $key => $field ) {
			woocommerce_form_field( $key, $field, $checkout->get_value( $key ) );
		}
		?>
	</div>

	<?php do_action( 'woocommerce_after_checkout_billing_form', $checkout ); ?>
</div>

<?php if ( ! is_user_logged_in() && $checkout->is_registration_enabled() ) : ?>
	<div class="woocommerce-account-fields">
		<?php if ( ! $checkout->is_registration_required() ) : ?>

			<p class="form-row form-row-wide create-account">
				<label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
					<input class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" id="createaccount" <?php checked( ( true === $checkout->get_value( 'createaccount' ) || ( true === apply_filters( 'woocommerce_create_account_default_checked', false ) ) ), true ); ?> type="checkbox" name="createaccount" value="1" />
					<span><?php esc_html_e( 'Create an account?', 'woocommerce' ); ?></span>
				</label>
			</p>

		<?php endif; ?>

		<?php do_action( 'woocommerce_before_checkout_registration_form', $checkout ); ?>

		<?php if ( $checkout->get_checkout_fields( 'account' ) ) : ?>

			<div class="create-account">
				<?php foreach ( $checkout->get_checkout_fields( 'account' ) as $key => $field ) : ?>
					<?php woocommerce_form_field( $key, $field, $checkout->get_value( $key ) ); ?>
				<?php endforeach; ?>
				<div class="clear"></div>
			</div>

		<?php endif; ?>

		<?php do_action( 'woocommerce_after_checkout_registration_form', $checkout ); ?>
	</div>
<?php endif; ?>
